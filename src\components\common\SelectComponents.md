# 选择组件使用说明

## OrgSelect 机构选择组件

### 基本用法
```vue
<template>
  <OrgSelect 
    v-model="selectedOrgId" 
    from="account"
    @change="handleOrgChange"
  />
</template>

<script setup>
import OrgSelect from '@/components/common/OrgSelect.vue';

const selectedOrgId = ref();

const handleOrgChange = (value, org) => {
  console.log('选择的机构:', value, org);
};
</script>
```

### Props
- `excludeIds?: number[]` - 需要排除的机构ID列表
- `from?: string` - 来自哪个菜单路由，用于权限判断 ('org', 'user', 'account', 'product')
- `filterByUserOrg?: boolean` - 是否按用户所属机构过滤机构列表
- `enabledOnly?: boolean` - 只显示启用状态的机构，默认true

### Events
- `change: [value: number | undefined, org?: MomOrganization]` - 选择变化事件
- `save: [org: MomOrganization]` - 保存新机构事件
- `refresh: []` - 刷新事件

## BrokerSelect 经纪商选择组件

### 基本用法
```vue
<template>
  <BrokerSelect 
    v-model="selectedBrokerId" 
    from="account"
    :broker-type="BrokerTypeEnum.股票"
    @change="handleBrokerChange"
  />
</template>

<script setup>
import BrokerSelect from '@/components/common/BrokerSelect.vue';
import { BrokerTypeEnum } from '../../../../xtrade-sdk/dist';

const selectedBrokerId = ref();

const handleBrokerChange = (value, broker) => {
  console.log('选择的经纪商:', value, broker);
};
</script>
```

### Props
- `excludeIds?: number[]` - 需要排除的经纪商ID列表
- `brokerType?: BrokerTypeEnum` - 经纪商类型过滤
- `from?: string` - 来自哪个菜单路由，用于权限判断 ('broker', 'account')

### Events
- `change: [value: number | undefined, broker?: MomBroker]` - 选择变化事件
- `save: [broker: MomBroker]` - 保存新经纪商事件
- `refresh: []` - 刷新事件

## TerminalSelect 终端选择组件

### 基本用法
```vue
<template>
  <TerminalSelect 
    v-model="selectedTerminalId" 
    from="account"
    :interface-type="TerminalType.StockTerminal.Value"
    @change="handleTerminalChange"
  />
</template>

<script setup>
import TerminalSelect from '@/components/common/TerminalSelect.vue';
import { TerminalType } from '../../../../xtrade-sdk/dist';

const selectedTerminalId = ref();

const handleTerminalChange = (value, terminal) => {
  console.log('选择的终端:', value, terminal);
};
</script>
```

### Props
- `excludeIds?: number[]` - 需要排除的终端ID列表
- `interfaceType?: number` - 接口类型过滤
- `enabledOnly?: boolean` - 只显示启用状态的终端，默认true
- `from?: string` - 来自哪个菜单路由，用于权限判断 ('terminal', 'account')

### Events
- `change: [value: number | undefined, terminal?: MomTerminal]` - 选择变化事件
- `save: [terminal: MomTerminal]` - 保存新终端事件
- `refresh: []` - 刷新事件

## 使用场景

### 创建/修改账号时
```vue
<template>
  <el-form>
    <el-form-item label="所属机构">
      <OrgSelect v-model="form.orgId" from="account" />
    </el-form-item>
    <el-form-item label="经纪商">
      <BrokerSelect v-model="form.brokerId" from="account" />
    </el-form-item>
    <el-form-item label="终端">
      <TerminalSelect v-model="form.terminalId" from="account" />
    </el-form-item>
  </el-form>
</template>
```

### 创建/修改产品时
```vue
<template>
  <el-form>
    <el-form-item label="所属机构">
      <OrgSelect v-model="form.orgId" from="product" />
    </el-form-item>
  </el-form>
</template>
```

### 创建/修改用户时
```vue
<template>
  <el-form>
    <el-form-item label="所属机构">
      <OrgSelect v-model="form.orgId" from="user" />
    </el-form-item>
  </el-form>
</template>
```
