<template>
  <div>
    <el-select
      ref="selectRef"
      v-model="model"
      placeholder="请选择机构"
      filterable
      clearable
      :loading="loading"
      @change="handleChange"
    >
      <el-option
        v-for="org in filteredOrgs"
        :key="org.id"
        :label="org.orgName"
        :value="org.id"
      />
      <template v-if="canCreate" #footer>
        <div p-2>
          <el-button type="primary" size="small" w-full @click="handleAddOrg">
            <i class="iconfont icon-add" mr-1></i>
            添加机构
          </el-button>
        </div>
      </template>
    </el-select>
    <!-- 新建机构对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新建机构"
      width="600px"
      draggable
      class="typical-dialog"
      destroy-on-close
      append-to-body
    >
      <OrgDialog
        ref="createOrgFormRef"
        v-if="showCreateDialog"
        v-model="showCreateDialog"
        @success="handleCreateOrgSaved"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  computed,
  onMounted,
  ref,
  shallowRef,
  watch,
  defineAsyncComponent,
  useTemplateRef,
} from 'vue';
import { ElMessage } from 'element-plus';
import { AdminService } from '@/api';
import { type MomOrganization } from '../../../../xtrade-sdk/dist';
import { getUser, hasPermission } from '@/script';
import {
  MenuPermitOrganizationManagement,
  MenuPermitAccountManagement,
  MenuPermitProductManagement,
  MenuPermitUserManagement,
} from '@/enum';

const OrgDialog = defineAsyncComponent(
  () => import('../OrgView/OrgDialog.vue'),
);

interface Props {
  /** 需要排除的机构ID列表 */
  excludeIds?: number[];
  /** 来自哪个菜单路由，用于权限判断 */
  from?: string;
  /** 是否按用户所属机构过滤机构列表 */
  filterByUserOrg?: boolean;
  /** 只显示启用状态的机构 */
  enabledOnly?: boolean;
}

const { 
  excludeIds = [], 
  from, 
  filterByUserOrg = false,
  enabledOnly = true 
} = defineProps<Props>();

const emit = defineEmits<{
  change: [value: number | undefined, org?: MomOrganization];
  save: [org: MomOrganization];
  refresh: [];
}>();

const model = defineModel<number | undefined>();
const selectRef = useTemplateRef('selectRef');

// 响应式数据
const loading = ref(false);
const allOrgs = shallowRef<MomOrganization[]>([]);
const showCreateDialog = ref(false);

// 当前用户
const currentUser = getUser()!;

const canCreate = computed(() => {
  // 机构管理表单选择机构时，肯定有权限
  if (from == 'org') {
    return hasPermission(MenuPermitOrganizationManagement.创建);
  } else if (from == 'user') {
    // 用户管理选择机构
    return hasPermission(MenuPermitUserManagement.创建);
  } else if (from == 'account') {
    return hasPermission(MenuPermitAccountManagement.创建账号);
  } else if (from == 'product') {
    return hasPermission(MenuPermitProductManagement.创建产品);
  } else {
    console.warn('OrgSelect: 未设置创建权限判断依据');
    return false;
  }
});

// 计算属性：过滤后的机构列表
const filteredOrgs = computed(() => {
  let orgs = allOrgs.value;

  // 按用户所属机构过滤
  if (filterByUserOrg) {
    orgs = orgs.filter(o => o.id === currentUser.orgId);
  }

  // 排除指定的机构ID
  if (excludeIds.length > 0) {
    orgs = orgs.filter(o => !excludeIds.includes(o.id));
  }

  // 只显示启用状态的机构
  if (enabledOnly) {
    orgs = orgs.filter(o => o.status === 1);
  }

  return orgs;
});

// 监听过滤条件变化，重新验证当前选择
watch(
  () => [filterByUserOrg, excludeIds, enabledOnly],
  () => {
    // 如果当前选择的机构不在新的过滤结果中，清空选择
    if (model.value) {
      const isCurrentOrgValid = filteredOrgs.value.some(o => o.id === model.value);
      if (!isCurrentOrgValid) {
        model.value = undefined;
      }
    }
  },
  { deep: true }
);

onMounted(() => {
  loadOrgs();
});

// 处理选择变化
const handleChange = (value: number | undefined) => {
  emit(
    'change',
    value,
    allOrgs.value.find(o => o.id === value),
  );
  selectRef.value?.blur();
};

// 加载机构列表
const loadOrgs = async () => {
  loading.value = true;
  try {
    const orgs = await AdminService.getOrgs();
    allOrgs.value = orgs || [];
  } catch (error) {
    console.error('加载机构列表失败:', error);
    ElMessage.error('加载机构列表失败');
  }
  loading.value = false;
};

// 处理添加机构
const handleAddOrg = () => {
  selectRef.value?.blur();
  showCreateDialog.value = true;
};

const handleCreateOrgSaved = async () => {
  await loadOrgs();
  emit('refresh');
  selectRef.value?.blur();
};
</script>

<style scoped></style>
